import {
    safeTimestampToNumber,
    safeTimestampToDate,
    hasTimestampExpired,
    getMillisecondsUntilTimestamp,
} from "../timestampHelpers";
import { beforeEach, describe, expect, it, vi } from "vitest";

describe("timestampHelpers", () => {
    let now: number;
    let futureTime: number;
    let pastTime: number;

    beforeEach(() => {
        // Use the mocked time from our setup
        now = Date.now();
        futureTime = now + 60000; // 1 minute in the future
        pastTime = now - 60000; // 1 minute in the past
    });

    describe("safeTimestampToNumber", () => {
        it("should return 0 for null or undefined", () => {
            expect(safeTimestampToNumber(null)).toBe(0);
            expect(safeTimestampToNumber(undefined)).toBe(0);
        });

        it("should return the same number for number input", () => {
            expect(safeTimestampToNumber(1234567890)).toBe(1234567890);
            expect(safeTimestampToNumber(0)).toBe(0);
        });

        it("should convert BigInt to number", () => {
            expect(safeTimestampToNumber(BigInt(1234567890))).toBe(1234567890);
        });

        it("should parse numeric strings", () => {
            expect(safeTimestampToNumber("1234567890")).toBe(1234567890);
            expect(safeTimestampToNumber("  1234567890  ")).toBe(1234567890);
        });

        it("should parse ISO date strings", () => {
            const isoString = "2024-01-15T12:00:00.000Z";
            const expectedTimestamp = Date.parse(isoString);
            expect(safeTimestampToNumber(isoString)).toBe(expectedTimestamp);
        });

        it("should return 0 for invalid strings", () => {
            expect(safeTimestampToNumber("invalid")).toBe(0);
            expect(safeTimestampToNumber("")).toBe(0);
            expect(safeTimestampToNumber("abc123")).toBe(0);
        });
    });

    describe("safeTimestampToDate", () => {
        it("should return null for invalid timestamps", () => {
            expect(safeTimestampToDate(null)).toBeNull();
            expect(safeTimestampToDate(undefined)).toBeNull();
            expect(safeTimestampToDate("invalid")).toBeNull();
        });

        it("should return valid Date for valid timestamps", () => {
            const timestamp = 1705320000000; // 2024-01-15T12:00:00.000Z
            const result = safeTimestampToDate(timestamp);
            expect(result).toBeInstanceOf(Date);
            expect(result?.getTime()).toBe(timestamp);
        });

        it("should handle BigInt timestamps", () => {
            const timestamp = BigInt(1705320000000);
            const result = safeTimestampToDate(timestamp);
            expect(result).toBeInstanceOf(Date);
            expect(result?.getTime()).toBe(1705320000000);
        });

        it("should handle string timestamps", () => {
            const result = safeTimestampToDate("1705320000000");
            expect(result).toBeInstanceOf(Date);
            expect(result?.getTime()).toBe(1705320000000);
        });
    });

    describe("hasTimestampExpired", () => {
        it("should return false for invalid timestamps", () => {
            expect(hasTimestampExpired(null)).toBe(false);
            expect(hasTimestampExpired(undefined)).toBe(false);
            expect(hasTimestampExpired("invalid")).toBe(false);
        });

        it("should return true for past timestamps", () => {
            expect(hasTimestampExpired(pastTime)).toBe(true);
        });

        it("should return false for future timestamps", () => {
            expect(hasTimestampExpired(futureTime)).toBe(false);
        });

        it("should return true for current time", () => {
            expect(hasTimestampExpired(now)).toBe(true);
        });
    });

    describe("getMillisecondsUntilTimestamp", () => {
        it("should return -1 for invalid timestamps", () => {
            expect(getMillisecondsUntilTimestamp(null)).toBe(-1);
            expect(getMillisecondsUntilTimestamp(undefined)).toBe(-1);
            expect(getMillisecondsUntilTimestamp("invalid")).toBe(-1);
        });

        it("should return positive value for future timestamps", () => {
            const result = getMillisecondsUntilTimestamp(futureTime);
            expect(result).toBeGreaterThan(0);
            expect(result).toBeCloseTo(60000, -2); // Within 100ms tolerance
        });

        it("should return negative value for past timestamps", () => {
            const result = getMillisecondsUntilTimestamp(pastTime);
            expect(result).toBeLessThan(0);
            expect(result).toBeCloseTo(-60000, -2); // Within 100ms tolerance
        });

        it("should handle BigInt timestamps", () => {
            const result = getMillisecondsUntilTimestamp(BigInt(futureTime));
            expect(result).toBeGreaterThan(0);
        });
    });
});
