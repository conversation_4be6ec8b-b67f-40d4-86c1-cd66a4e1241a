import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render } from "@testing-library/react";
import { renderWithProviders } from "@/test/test-utils";
import SocketManager from "./SocketManager";

// Mock all external dependencies
vi.mock("@/helpers/api", () => ({
    api: {
        shrine: {
            getActiveBuff: {
                key: vi.fn(() => ["shrine", "getActiveBuff"]),
            },
        },
        chat: {
            getHistory: {
                queryKey: vi.fn(({ input }) => ["chat", "getHistory", input]),
            },
        },
    },
}));

vi.mock("@/hooks/useCheckMobileScreen", () => ({
    default: vi.fn(),
}));

vi.mock("@tanstack/react-query", async (importOriginal) => {
    const actual = await importOriginal();
    return {
        ...actual,
        useQueryClient: vi.fn(),
    };
});

vi.mock("react-router-dom", async (importOriginal) => {
    const actual = await importOriginal();
    return {
        ...actual,
        useLocation: vi.fn(),
    };
});

vi.mock("./app/store/stores", () => ({
    useAuthStore: vi.fn(),
    useNormalStore: vi.fn(),
    usePersistStore: vi.fn(),
    useSocketStore: vi.fn(),
}));

vi.mock("./assets/sounds/chatMsg.mp3", () => ({
    default: "mocked-audio-file.mp3",
}));

// Audio is mocked globally in setup.ts

// Import mocked modules
import { api } from "@/helpers/api";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { useQueryClient } from "@tanstack/react-query";
import { useLocation } from "react-router-dom";
import { useAuthStore, useNormalStore, usePersistStore, useSocketStore } from "./app/store/stores";

describe("SocketManager Component", () => {
    const mockQueryClient = {
        invalidateQueries: vi.fn(),
        setQueryData: vi.fn(),
    };

    const mockSocket = {
        on: vi.fn(),
        off: vi.fn(),
    };

    const mockUseNormalStore = {
        addUnreadChatMessages: vi.fn(),
    };

    const mockUsePersistStore = {
        muteChat: false,
        hideGlobalChat: false,
    };

    const mockUseSocketStore = {
        socket: mockSocket,
        fetchSocket: vi.fn(),
        setIsSocketConnected: vi.fn(),
    };

    const mockUseLocation = {
        pathname: "/home",
    };

    beforeEach(() => {
        vi.clearAllMocks();

        // Setup default mocks
        (useQueryClient as any).mockReturnValue(mockQueryClient);
        (useNormalStore as any).mockReturnValue(mockUseNormalStore);
        (usePersistStore as any).mockReturnValue(mockUsePersistStore);
        (useSocketStore as any).mockReturnValue(mockUseSocketStore);
        (useAuthStore as any).mockReturnValue(false); // authed = false
        (useLocation as any).mockReturnValue(mockUseLocation);
        (useCheckMobileScreen as any).mockReturnValue(false);
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    it("renders without crashing and returns null", () => {
        const { container } = renderWithProviders(<SocketManager />);
        expect(container.firstChild).toBeNull();
    });

    it("sets up socket event listeners when socket is available", () => {
        renderWithProviders(<SocketManager />);

        expect(mockSocket.on).toHaveBeenCalledWith("chat message", expect.any(Function));
        expect(mockSocket.on).toHaveBeenCalledWith("connect", expect.any(Function));
        expect(mockSocket.on).toHaveBeenCalledWith("disconnect", expect.any(Function));
        expect(mockSocket.on).toHaveBeenCalledWith("messagesRemoved", expect.any(Function));
    });

    it("removes socket event listeners on cleanup", () => {
        const { unmount } = renderWithProviders(<SocketManager />);

        unmount();

        expect(mockSocket.off).toHaveBeenCalledWith("chat message", expect.any(Function));
        expect(mockSocket.off).toHaveBeenCalledWith("connect", expect.any(Function));
        expect(mockSocket.off).toHaveBeenCalledWith("disconnect", expect.any(Function));
        expect(mockSocket.off).toHaveBeenCalledWith("messagesRemoved", expect.any(Function));
    });

    it("fetches socket when user is authenticated and socket is not available", () => {
        (useAuthStore as any).mockReturnValue(true); // authed = true
        (useSocketStore as any).mockReturnValue({
            ...mockUseSocketStore,
            socket: undefined,
        });

        renderWithProviders(<SocketManager />);

        expect(mockUseSocketStore.fetchSocket).toHaveBeenCalled();
    });

    it("does not fetch socket when user is not authenticated", () => {
        (useSocketStore as any).mockReturnValue({
            ...mockUseSocketStore,
            socket: undefined,
        });

        renderWithProviders(<SocketManager />);

        expect(mockUseSocketStore.fetchSocket).not.toHaveBeenCalled();
    });

    it("does not fetch socket when socket already exists", () => {
        (useAuthStore as any).mockReturnValue(true); // authed = true

        renderWithProviders(<SocketManager />);

        expect(mockUseSocketStore.fetchSocket).not.toHaveBeenCalled();
    });

    it("handles chat message event correctly", () => {
        renderWithProviders(<SocketManager />);

        // Get the chat message handler
        const chatMessageHandler = mockSocket.on.mock.calls.find((call) => call[0] === "chat message")?.[1];

        expect(chatMessageHandler).toBeDefined();

        const testMessage = {
            userId: 123,
            message: "Test message",
            chatRoomId: 1,
        };

        // Simulate chat message event
        if (chatMessageHandler) {
            chatMessageHandler(testMessage);

            expect(mockQueryClient.setQueryData).toHaveBeenCalledWith(
                ["chat", "getHistory", { roomId: "1", limit: 200 }],
                expect.any(Function)
            );
        }
    });

    it("invalidates shrine buff query for special shrine message", () => {
        renderWithProviders(<SocketManager />);

        const chatMessageHandler = mockSocket.on.mock.calls.find((call) => call[0] === "chat message")?.[1];

        const shrineMessage = {
            userId: 0,
            message: "The Shrine daily donation goal has been reached. Global buffs are now active!",
            chatRoomId: 1,
        };

        if (chatMessageHandler) {
            chatMessageHandler(shrineMessage);

            expect(mockQueryClient.invalidateQueries).toHaveBeenCalledWith({
                queryKey: ["shrine", "getActiveBuff"],
            });
        }
    });

    it("adds unread chat messages for mobile users not in chat", () => {
        (useCheckMobileScreen as any).mockReturnValue(true);

        renderWithProviders(<SocketManager />);

        const chatMessageHandler = mockSocket.on.mock.calls.find((call) => call[0] === "chat message")?.[1];

        const testMessage = {
            userId: 123,
            message: "Test message",
            chatRoomId: 1,
        };

        if (chatMessageHandler) {
            chatMessageHandler(testMessage);

            expect(mockUseNormalStore.addUnreadChatMessages).toHaveBeenCalled();
        }
    });

    it("does not add unread messages for mobile users in chat", () => {
        (useCheckMobileScreen as any).mockReturnValue(true);
        (useLocation as any).mockReturnValue({ pathname: "/chat" });

        renderWithProviders(<SocketManager />);

        const chatMessageHandler = mockSocket.on.mock.calls.find((call) => call[0] === "chat message")?.[1];

        const testMessage = {
            userId: 123,
            message: "Test message",
            chatRoomId: 1,
        };

        if (chatMessageHandler) {
            chatMessageHandler(testMessage);

            expect(mockUseNormalStore.addUnreadChatMessages).not.toHaveBeenCalled();
        }
    });

    it("plays audio when chat is not muted and global chat is not hidden", () => {
        renderWithProviders(<SocketManager />);

        const chatMessageHandler = mockSocket.on.mock.calls.find((call) => call[0] === "chat message")?.[1];

        const testMessage = {
            userId: 123,
            message: "Test message",
            chatRoomId: 2, // Not room 1 to avoid mobile logic
        };

        if (chatMessageHandler) {
            chatMessageHandler(testMessage);

            // Get the audio instance that was created
            const audioInstance = (global.Audio as any).mock.results[0].value;
            expect(audioInstance.play).toHaveBeenCalled();
        }
    });

    it("does not play audio when chat is muted", () => {
        (usePersistStore as any).mockReturnValue({
            ...mockUsePersistStore,
            muteChat: true,
        });

        renderWithProviders(<SocketManager />);

        const chatMessageHandler = mockSocket.on.mock.calls.find((call) => call[0] === "chat message")?.[1];

        const testMessage = {
            userId: 123,
            message: "Test message",
            chatRoomId: 2,
        };

        if (chatMessageHandler) {
            chatMessageHandler(testMessage);

            // Audio should not be played when muted
            const audioInstance = (global.Audio as any).mock.results[0].value;
            expect(audioInstance.play).not.toHaveBeenCalled();
        }
    });

    it("does not play audio when global chat is hidden", () => {
        (usePersistStore as any).mockReturnValue({
            ...mockUsePersistStore,
            hideGlobalChat: true,
        });

        renderWithProviders(<SocketManager />);

        const chatMessageHandler = mockSocket.on.mock.calls.find((call) => call[0] === "chat message")?.[1];

        const testMessage = {
            userId: 123,
            message: "Test message",
            chatRoomId: 2,
        };

        if (chatMessageHandler) {
            chatMessageHandler(testMessage);

            // Audio should not be played when global chat is hidden
            const audioInstance = (global.Audio as any).mock.results[0].value;
            expect(audioInstance.play).not.toHaveBeenCalled();
        }
    });

    it("handles socket connect event", () => {
        renderWithProviders(<SocketManager />);

        const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === "connect")?.[1];

        if (connectHandler) {
            connectHandler();

            expect(mockUseSocketStore.setIsSocketConnected).toHaveBeenCalledWith(true);
        }
    });

    it("handles socket disconnect event", () => {
        renderWithProviders(<SocketManager />);

        const disconnectHandler = mockSocket.on.mock.calls.find((call) => call[0] === "disconnect")?.[1];

        if (disconnectHandler) {
            disconnectHandler();

            expect(mockUseSocketStore.setIsSocketConnected).toHaveBeenCalledWith(false);
        }
    });

    it("handles messagesRemoved event", () => {
        renderWithProviders(<SocketManager />);

        const messagesRemovedHandler = mockSocket.on.mock.calls.find((call) => call[0] === "messagesRemoved")?.[1];

        if (messagesRemovedHandler) {
            messagesRemovedHandler();

            expect(mockQueryClient.invalidateQueries).toHaveBeenCalledWith({
                queryKey: ["chat", "getHistory", { roomId: "1", limit: 200 }],
            });
        }
    });

    it("does not set up event listeners when socket is not available", () => {
        (useSocketStore as any).mockReturnValue({
            ...mockUseSocketStore,
            socket: undefined,
        });

        renderWithProviders(<SocketManager />);

        expect(mockSocket.on).not.toHaveBeenCalled();
    });
});
