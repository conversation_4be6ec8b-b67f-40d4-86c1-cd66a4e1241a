import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { renderWithProviders } from "@/test/test-utils";
import App from "./App";

// Mock all external dependencies
vi.mock("@/app/fetchGameConfig", () => ({
    fetchGameConfig: vi.fn(),
}));

vi.mock("@/app/firebase", () => ({
    sendTokenToServer: vi.fn(),
}));

vi.mock("@/app/store/stores", () => ({
    useAuthStore: vi.fn(),
    useNormalStore: vi.fn(),
    usePersistStore: vi.fn(),
}));

vi.mock("@/components/Layout/ErrorBoundary", () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="error-boundary">{children}</div>,
}));

vi.mock("@/components/MaintenanceMode", () => ({
    default: () => <div data-testid="maintenance-mode">Maintenance Mode</div>,
}));

vi.mock("@/components/Spinners/Spinner", () => ({
    default: () => <div data-testid="loading-spinner">Loading...</div>,
}));

vi.mock("@/components/ToastManager", () => ({
    default: () => <div data-testid="toast-manager">Toast Manager</div>,
}));

vi.mock("@/components/Tooltips", () => ({
    default: () => <div data-testid="tooltips">Tooltips</div>,
}));

vi.mock("@/lib/utils", () => ({
    vd: vi.fn(),
}));

vi.mock("ag-grid-community", () => ({
    AllCommunityModule: {},
    ClientSideRowModelModule: {},
    ModuleRegistry: {
        registerModules: vi.fn(),
    },
    provideGlobalGridOptions: vi.fn(),
}));

vi.mock("posthog-js", () => ({
    default: {
        capture: vi.fn(),
    },
}));

vi.mock("react-router-dom", async (importOriginal) => {
    const actual = await importOriginal();
    return {
        ...actual,
        Outlet: () => <div data-testid="outlet">Outlet Content</div>,
        useLocation: vi.fn(),
    };
});

vi.mock("./SocketManager", () => ({
    default: () => <div data-testid="socket-manager">Socket Manager</div>,
}));

// Import mocked modules
import { fetchGameConfig } from "@/app/fetchGameConfig";
import { sendTokenToServer } from "@/app/firebase";
import { useAuthStore, useNormalStore, usePersistStore } from "@/app/store/stores";
import { vd } from "@/lib/utils";
import posthog from "posthog-js";
import { useLocation } from "react-router-dom";

describe("App Component", () => {
    const mockUseNormalStore = {
        refreshPagePrompt: false,
        setRefreshPagePrompt: vi.fn(),
        setShowDailyModal: vi.fn(),
        messagingToken: null,
        isInMaintenance: false,
    };

    const mockUsePersistStore = {
        colourTheme: "dark",
        gameConfig: null,
    };

    const mockUseAuthStore = vi.fn();
    const mockUseLocation = {
        pathname: "/home",
    };

    beforeEach(() => {
        vi.clearAllMocks();

        // Setup default mocks
        (useNormalStore as any).mockReturnValue(mockUseNormalStore);
        (usePersistStore as any).mockReturnValue(mockUsePersistStore);
        (useAuthStore as any).mockReturnValue(false); // authed = false
        (useLocation as any).mockReturnValue(mockUseLocation);

        // Mock document.body.classList
        document.body.classList.add = vi.fn();
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    it("renders the main app structure when not in maintenance mode", () => {
        renderWithProviders(<App />);

        expect(screen.getByTestId("socket-manager")).toBeInTheDocument();
        expect(screen.getByTestId("error-boundary")).toBeInTheDocument();
        expect(screen.getByTestId("outlet")).toBeInTheDocument();
        expect(screen.getByTestId("toast-manager")).toBeInTheDocument();
        expect(screen.getByTestId("tooltips")).toBeInTheDocument();
    });

    it("renders maintenance mode when isInMaintenance is true", () => {
        (useNormalStore as any).mockReturnValue({
            ...mockUseNormalStore,
            isInMaintenance: true,
        });

        renderWithProviders(<App />);

        expect(screen.getByTestId("maintenance-mode")).toBeInTheDocument();
        expect(screen.queryByTestId("socket-manager")).not.toBeInTheDocument();
        expect(screen.queryByTestId("outlet")).not.toBeInTheDocument();
    });

    it("adds colour theme to document body on mount", () => {
        renderWithProviders(<App />);

        expect(document.body.classList.add).toHaveBeenCalledWith("dark");
    });

    it("captures pageview when location changes", () => {
        renderWithProviders(<App />);

        expect(posthog.capture).toHaveBeenCalledWith("$pageview");
    });

    it("fetches game config when not available", () => {
        renderWithProviders(<App />);

        expect(fetchGameConfig).toHaveBeenCalled();
    });

    it("does not fetch game config when already available", () => {
        (usePersistStore as any).mockReturnValue({
            ...mockUsePersistStore,
            gameConfig: { version: "1.0.0" },
        });

        renderWithProviders(<App />);

        expect(fetchGameConfig).not.toHaveBeenCalled();
    });

    it("calls vd function with setShowDailyModal", () => {
        renderWithProviders(<App />);

        expect(vd).toHaveBeenCalledWith(mockUseNormalStore.setShowDailyModal);
    });

    it("resets refresh page prompt when it's true", () => {
        (useNormalStore as any).mockReturnValue({
            ...mockUseNormalStore,
            refreshPagePrompt: true,
        });

        renderWithProviders(<App />);

        expect(mockUseNormalStore.setRefreshPagePrompt).toHaveBeenCalledWith(false);
    });

    it("sends token to server when user is authenticated and has messaging token", () => {
        const mockToken = "test-messaging-token";

        (useAuthStore as any).mockReturnValue(true); // authed = true
        (useNormalStore as any).mockReturnValue({
            ...mockUseNormalStore,
            messagingToken: mockToken,
        });

        renderWithProviders(<App />);

        expect(sendTokenToServer).toHaveBeenCalledWith(mockToken);
    });

    it("does not send token when user is not authenticated", () => {
        (useNormalStore as any).mockReturnValue({
            ...mockUseNormalStore,
            messagingToken: "test-token",
        });

        renderWithProviders(<App />);

        expect(sendTokenToServer).not.toHaveBeenCalled();
    });

    it("does not send token when messaging token is not available", () => {
        (useAuthStore as any).mockReturnValue(true); // authed = true

        renderWithProviders(<App />);

        expect(sendTokenToServer).not.toHaveBeenCalled();
    });

    it("renders loading spinner in Suspense fallback", () => {
        renderWithProviders(<App />);

        // The loading spinner should be available as a fallback
        expect(screen.getByTestId("outlet")).toBeInTheDocument();
    });
});
