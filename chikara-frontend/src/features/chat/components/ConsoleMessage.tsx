import news from "@/constants/news";
import parseJson from "@/helpers/parseJson";
import { Link } from "react-router-dom";
import { formatTimeToNow } from "@/helpers/dateHelpers";
import { formatCurrency } from "@/utils/currencyHelpers";
import { ChatMessage } from "../types/chat";

interface AdministratorMessageProps {
    children: React.ReactNode;
    msg: ChatMessage;
}

const AdministratorMessage = ({ children, msg }: AdministratorMessageProps) => (
    <div className="chatMessageWrapperAdmin relative mt-2 min-h-[74px] rounded-lg py-2 pr-2 pl-20 md:min-h-[80px] dark:text-slate-300">
        <div className="chatMessageAvatar absolute size-14 rounded-md ring-2 ring-red-600 md:size-16">
            <div className="relative">
                <img
                    src="https://d13cmcqz8qkryo.cloudfront.net/static/misc/adminAvatar.png"
                    loading="lazy"
                    alt="Console Message"
                    className="size-full rounded-md object-cover"
                />
            </div>
        </div>

        <div className="flex flex-row">
            <p className="my-auto font-semibold text-gray-800 text-sm dark:font-normal dark:text-red-600">
                Administrator
            </p>
            <small className="my-auto mr-2 ml-auto text-gray-600 text-xs dark:text-slate-300">
                {formatTimeToNow(msg.createdAt)} ago
            </small>
        </div>
        {children}
    </div>
);

interface StandardPlainMessageProps {
    children: React.ReactNode;
}

const StandardPlainMessage = ({ children }: StandardPlainMessageProps) => (
    <div className="mx-auto flex w-fit items-center gap-2">
        <div className="px-4! relative mt-2 flex min-h-[20px] w-fit flex-1 rounded-lg border-blue-600/50 border-x border-t border-b-2 bg-black/25 py-2 pl-2 shadow-xl md:min-h-[20px] dark:text-slate-300">
            <p className="m-auto text-center font-display text-sky-500 text-xs">{children}</p>
        </div>
    </div>
);

interface UserLinkProps {
    userId: number;
    username: string;
    className?: string;
}

const UserLink = ({
    userId,
    username,
    className = "text-blue-500 text-stroke-sm dark:text-blue-400",
}: UserLinkProps) => (
    <Link className={className} to={`/profile/${userId}`}>
        {username}
    </Link>
);

interface PatchNotesMessageProps {
    id?: number;
}

const PatchNotesMessage = ({ id }: PatchNotesMessageProps) => {
    const position = id || 0;
    const post = news?.find((el) => el.id === position);
    const isUndefined = !post;

    return (
        <div className="my-auto w-[90%] break-words text-gray-700 text-shadow text-xs">
            <p className="my-auto mb-2 w-[90%] break-words text-gray-700 text-stroke-sm text-xs dark:text-custom-yellow">
                {/* Game updated! {!patchNotesUndefined && notes[0]?.version} */}
                Game updated!
            </p>
            {isUndefined ? (
                <p
                    className="cursor-pointer text-sky-500 text-stroke-sm text-xs"
                    onClick={() => window.location.reload()}
                >
                    Please refresh to update and view the newest patch notes.
                </p>
            ) : (
                <Link className="text-sky-600 text-sm underline" to={`/news/${post?.href || ""}`}>
                    View patch notes
                </Link>
            )}
        </div>
    );
};

// Main Announcement Handler
const getAnnouncementMessage = (type: string, msg: ChatMessage) => {
    const details = parseJson(msg.message) || {};

    const messageHandlers: Record<string, () => JSX.Element | null> = {
        consoleMessage: () => <StandardPlainMessage>{msg.message}</StandardPlainMessage>,

        npcDefeated: () => (
            <StandardPlainMessage>
                <UserLink userId={details.id} username={details.username} /> has defeated the boss ${details.targetName}
                !
            </StandardPlainMessage>
        ),

        rareItemDrop: () => (
            <StandardPlainMessage>
                <UserLink userId={details.id} username={details.username} /> has received a rare {details.itemName} item
                drop!
            </StandardPlainMessage>
        ),

        levelCapReached: () => (
            <StandardPlainMessage>
                <UserLink userId={details.id} username={details.username} /> has reached level 40! Congratulations!
            </StandardPlainMessage>
        ),

        patchNotes: () => (
            <AdministratorMessage msg={msg}>
                <PatchNotesMessage id={details.id} />
            </AdministratorMessage>
        ),

        newSuggestion: () => (
            <AdministratorMessage msg={msg}>
                <div className="my-auto w-[90%] break-words text-gray-700 text-shadow text-xs">
                    <p className="my-auto mb-2 w-[90%] break-words text-gray-200 text-stroke-sm text-xs">
                        A new suggestion was created!
                    </p>
                    <Link
                        to={{
                            pathname: "/suggestions",
                            search: `?id=${details.id}`,
                        }}
                    >
                        <p className="truncate text-sky-600 text-sm underline">{details.title}</p>
                    </Link>
                </div>
            </AdministratorMessage>
        ),

        shrineGoalReached: () => (
            <StandardPlainMessage>
                <p className="text-custom-yellow text-xs">
                    The Shrine daily donation goal has been reached. Global buffs are now active!
                </p>
            </StandardPlainMessage>
        ),

        shrineGoalReset: () => (
            <StandardPlainMessage>
                The Shrine goal has reset! Today&apos;s donation goal is {formatCurrency(details.goal)}
            </StandardPlainMessage>
        ),

        lotteryEndingSoon: () => (
            <StandardPlainMessage>The current lottery is ending in 30 minutes!</StandardPlainMessage>
        ),

        lotteryStarted: () => (
            <StandardPlainMessage>
                A new lottery draw has started at the casino. The winner will be announced at 6:30PM tomorrow.
            </StandardPlainMessage>
        ),

        lotteryWinner: () => (
            <StandardPlainMessage>
                <UserLink userId={details.id} username={details.username} /> has won the lottery prize of{" "}
                {formatCurrency(details.prizeAmount)}! A new lottery draw will start in 5 minutes.
            </StandardPlainMessage>
        ),

        shopOpenSoon: () => <StandardPlainMessage>The Sunday shop will open in 30 minutes!</StandardPlainMessage>,

        shopNowOpen: () => <StandardPlainMessage>The Sunday shop is now open for trading!</StandardPlainMessage>,

        newUserRegistered: () => (
            <AdministratorMessage msg={msg}>
                <p className="my-auto w-[90%] break-words text-gray-700 text-xs dark:text-slate-300">
                    New student <UserLink userId={details.id} username={details.username} /> has joined the academy!
                </p>
            </AdministratorMessage>
        ),
    };

    return messageHandlers[type] ? messageHandlers[type]() : null;
};

// Main Component
interface ConsoleMessageProps {
    msg: ChatMessage;
    type: string;
}

function ConsoleMessage({ msg, type }: ConsoleMessageProps) {
    const messageTypes = [
        "levelCapReached",
        "consoleMessage",
        "patchNotes",
        "newUserRegistered",
        "npcDefeated",
        "rareItemDrop",
        "shrineGoalReached",
        "shrineGoalReset",
        "newSuggestion",
        "lotteryEndingSoon",
        "lotteryStarted",
        "lotteryWinner",
        "shopOpenSoon",
        "shopNowOpen",
    ];

    if (!messageTypes.includes(type)) return null;

    return getAnnouncementMessage(type, msg);
}

export default ConsoleMessage;
