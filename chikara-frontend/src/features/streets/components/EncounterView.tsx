import { DisplayItem } from "@/components/DisplayItem";
import { characterManager } from "@/helpers/characterManager";
import { sceneManager } from "@/helpers/sceneManager";
import { cn } from "@/lib/utils";
import { formatCurrency } from "@/utils/currencyHelpers";
import { motion, useAnimation } from "framer-motion";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSessionStore } from "../../../app/store/stores";
import { type User } from "@/types/user";

interface EncounterViewProps {
    currentMap: User["roguelikeMap"];
}

export default function EncounterView({ currentMap }: EncounterViewProps) {
    const { setInEncounter } = useSessionStore();
    const controls = useAnimation();
    const navigate = useNavigate();

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 1 } },
    };

    const wipeVariants = {
        hidden: { width: "100%", left: 0 },
        visible: {
            width: 0,
            left: "50%",
            transition: { ease: "linear", duration: 0.75 },
        },
    };

    const imageVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.5 } },
    };

    const dialogueBoxVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.1 } },
    };

    useEffect(() => {
        controls.start("visible");
    }, [controls]);

    const showCorrectMugText = (mugAmount: number, characterName: string, jailed: boolean) => {
        if (mugAmount === 0) {
            if (jailed) {
                return `You were caught littering! You were sent to jail.`;
            }
            return `${characterName} went through your wallet but found nothing!`;
        }
        if (mugAmount > 0) {
            if (jailed) {
                return `You were caught littering! You received a ${formatCurrency(mugAmount)} fine and were sent to jail.`;
            }
            return `${characterName} stole ${formatCurrency(mugAmount)} from your wallet!`;
        }
        return "";
    };
    const hideCharacter = currentMap?.currentNodeDialogue?.isItemDrop;

    return (
        <motion.div className="shadow md:rounded-b-lg" variants={containerVariants} initial="hidden" animate={controls}>
            <motion.div className="wipe" variants={wipeVariants} initial="hidden" animate="visible" />
            <div className="relative h-full overflow-hidden md:h-full md:rounded-lg">
                <img
                    className="h-full object-cover md:h-full md:scale-105 md:rounded-lg"
                    src={sceneManager(currentMap?.currentNodeDialogue?.location)}
                    alt=""
                />
                <div className="absolute bottom-0 size-full bg-black opacity-20 md:rounded-b-lg"></div>
                <motion.div variants={dialogueBoxVariants} initial="hidden" animate="visible">
                    <div
                        className={cn(
                            "-translate-x-1/2 absolute left-1/2 z-50 w-[90%] skew-x-2 justify-end border-4 bg-slate-800 text-white opacity-95 shadow-lg md:h-44 md:skew-x-1",
                            hideCharacter ? "-translate-y-1/2 top-1/2 border-blue-500 md:w-2/4" : "bottom-14 md:w-3/4"
                        )}
                    >
                        {!hideCharacter && (
                            <div className="-top-12 -translate-x-1/2 -rotate-3 -skew-x-2 md:-top-14 md:-skew-x-3 absolute left-[4.2rem] z-50 h-14 w-28 justify-end border-4 bg-slate-800 text-center text-white md:left-20 md:h-16 md:w-32">
                                <p className="m-2 skew-x-2 text-2xl text-[#6dc7ff] md:skew-x-3 md:text-3xl">
                                    {currentMap?.currentNodeDialogue?.character}
                                </p>
                            </div>
                        )}

                        <div className="flex h-full flex-col">
                            <div className="-skew-x-2 md:-skew-x-3 mx-6 my-4 flex h-[52%] flex-col md:mx-8 md:h-[55%] md:text-lg xl:text-xl 2xl:text-2xl">
                                {!hideCharacter ? (
                                    <>
                                        {" "}
                                        {currentMap?.currentNodeDialogue?.jailed ? (
                                            <p className="mx-auto inline-block h-[70%]">Hey, stop right there!</p>
                                        ) : (
                                            <p
                                                className={cn(
                                                    (currentMap?.currentNodeDialogue?.line?.length ?? 0) < 16
                                                        ? "text-center"
                                                        : "text-left",
                                                    "inline-block h-[70%]"
                                                )}
                                            >
                                                {currentMap?.currentNodeDialogue?.line}
                                            </p>
                                        )}
                                    </>
                                ) : (
                                    <p className="mx-auto inline-block h-[70%]">
                                        You found an item while exploring the zone!
                                    </p>
                                )}

                                {currentMap?.currentNodeDialogue?.mugged ? (
                                    <>
                                        <p className="text-center text-red-500 md:mt-0">
                                            {showCorrectMugText(
                                                currentMap?.currentNodeDialogue?.rewards as number,
                                                currentMap?.currentNodeDialogue?.character || "",
                                                currentMap?.currentNodeDialogue?.jailed || false
                                            )}
                                            {currentMap?.currentNodeDialogue?.hospitalised && (
                                                <span> You were injured in the attack.</span>
                                            )}
                                        </p>
                                    </>
                                ) : (
                                    <div className="text-center text-yellow-400 md:mt-0">
                                        {currentMap?.currentNodeDialogue?.character === "Apollo" ? (
                                            <> You were healed by the dogs presence </>
                                        ) : (
                                            <>
                                                {" "}
                                                {currentMap?.currentNodeDialogue?.isItemDrop ? (
                                                    <div className="mt-1.5 flex">
                                                        <span className="mx-auto flex gap-2">
                                                            {" "}
                                                            You gained 1x{" "}
                                                            <DisplayItem
                                                                item={currentMap?.currentNodeDialogue?.rewards}
                                                                className="inline-block h-6 md:h-8"
                                                            />{" "}
                                                            <span className="text-sky-400 text-stroke-sm">
                                                                {
                                                                    (currentMap?.currentNodeDialogue?.rewards as any)
                                                                        ?.name
                                                                }
                                                            </span>
                                                        </span>
                                                    </div>
                                                ) : (
                                                    <>
                                                        You gained{" "}
                                                        {formatCurrency(
                                                            currentMap?.currentNodeDialogue?.rewards as number
                                                        )}
                                                    </>
                                                )}
                                                {currentMap?.currentNodeDialogue?.crateReward && (
                                                    <div className="mt-1.5 flex">
                                                        <span className="mx-auto flex gap-1 text-sm md:text-base">
                                                            {" "}
                                                            You found a
                                                            <DisplayItem
                                                                item={currentMap?.currentNodeDialogue?.crateReward}
                                                                className="inline-block h-6 md:h-6"
                                                            />{" "}
                                                            <span className="text-sky-400 text-sm text-stroke-sm md:text-base">
                                                                {currentMap?.currentNodeDialogue?.crateReward?.name}
                                                            </span>
                                                            <span className="hidden md:block">in the trash</span>
                                                        </span>
                                                    </div>
                                                )}
                                            </>
                                        )}
                                    </div>
                                )}
                            </div>
                            {currentMap?.currentNodeDialogue?.jailed ? (
                                <button
                                    className="-skew-x-2 md:-skew-x-3 mx-2 mb-[0.35rem] rounded-md border border-transparent bg-indigo-600 px-3 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mt-0 md:px-4"
                                    onClick={() => {
                                        setInEncounter(false);
                                        navigate("/jail");
                                    }}
                                >
                                    Go to jail
                                </button>
                            ) : (
                                <button
                                    className="-skew-x-2 md:-skew-x-3 mx-2 mb-[0.35rem] rounded-md border border-transparent bg-indigo-600 px-3 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mt-0 md:px-4"
                                    onClick={() => {
                                        setInEncounter(false);
                                    }}
                                >
                                    Return to streets
                                </button>
                            )}
                        </div>
                    </div>
                </motion.div>
                {!hideCharacter && (
                    <motion.div variants={imageVariants} initial="hidden" animate="visible">
                        <img
                            src={characterManager(currentMap?.currentNodeDialogue?.character)}
                            alt="Encounter character"
                            className={cn(
                                characterManager(currentMap?.currentNodeDialogue?.character, true),
                                "absolute z-0"
                            )}
                        />
                    </motion.div>
                )}
            </div>
        </motion.div>
    );
}
