import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import { renderWithProviders } from "@/test/test-utils";
import StatusMessageSetting from "../StatusMessageSetting";

// Mock the hooks
vi.mock("@/features/social/api/useUpdateStatusMessage", () => ({
    default: () => ({
        mutate: vi.fn(),
        isPending: false,
    }),
}));

describe("StatusMessageSetting", () => {
    it("should render without crashing when statusMessageUpdatedAt is null", () => {
        renderWithProviders(
            <StatusMessageSetting showOnlineStatus statusMessage="Test status" statusMessageUpdatedAt={null} />
        );

        expect(screen.getByText("Status Message")).toBeInTheDocument();
        expect(screen.getByText("Test status")).toBeInTheDocument();
    });

    it("should render without crashing when statusMessageUpdatedAt is a valid Date", () => {
        const testDate = new Date("2024-01-01T12:00:00Z");

        renderWithProviders(
            <StatusMessageSetting showOnlineStatus statusMessage="Test status" statusMessageUpdatedAt={testDate} />
        );

        expect(screen.getByText("Status Message")).toBeInTheDocument();
        expect(screen.getByText("Test status")).toBeInTheDocument();
        // Should show "Updated X time ago" text
        expect(screen.getByText(/Updated .* ago/)).toBeInTheDocument();
    });

    it("should not show updated time when statusMessageUpdatedAt is null", () => {
        renderWithProviders(
            <StatusMessageSetting showOnlineStatus statusMessage="Test status" statusMessageUpdatedAt={null} />
        );

        // Should not show "Updated X time ago" text
        expect(screen.queryByText(/Updated .* ago/)).not.toBeInTheDocument();
    });

    it("should handle empty status message", () => {
        renderWithProviders(
            <StatusMessageSetting showOnlineStatus statusMessage={null} statusMessageUpdatedAt={null} />
        );

        expect(screen.getByText("Status Message")).toBeInTheDocument();
        expect(screen.getByText("Set a status message for your friends to see.")).toBeInTheDocument();
    });
});
