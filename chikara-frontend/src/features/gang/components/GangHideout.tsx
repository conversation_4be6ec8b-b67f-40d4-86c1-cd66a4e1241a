import lifeEssenceImg from "@/assets/icons/UI/lifeEssence.png";
import respectImg from "@/assets/icons/UI/respect.png";
import yenImg from "@/assets/icons/UI/yen.png";
import Button from "@/components/Buttons/Button";
import { cn } from "@/lib/utils";
import { Check, Info, X } from "lucide-react";
import useUpgradeHideout from "../api/useUpgradeHideout";
import rawMaterialsImg from "../rawMaterials.png";
import { formatCurrency } from "@/utils/currencyHelpers";
import type { Gang } from "../types/gang";
import type { User } from "@/types/user";

interface GangHideoutProps {
    gang: Gang | null | undefined;
    currentUser: User;
}

interface Requirement {
    name: string;
    unlocked: boolean;
    image: string;
}

interface HideoutUnlock {
    unlockText: string;
    requirements: Requirement[];
    unlocked: boolean;
}

interface ResourceDisplayProps {
    name: string;
    amount: number;
    src: string;
    textColor?: string;
    disabled?: boolean;
    maxAmount?: number;
}

const GangHideout = ({ gang, currentUser }: GangHideoutProps) => {
    const { upgradeHideout } = useUpgradeHideout();

    const hideoutUnlocks: HideoutUnlock[] = [
        {
            unlockText: "Unlocks Chatroom + Gang Icon",
            requirements: [
                {
                    name: "3 Gang Members",
                    unlocked: gang?.gang_member?.length >= 3,
                    image: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/UX78bPX.png`,
                },
                {
                    name: "150 Raw Materials",
                    unlocked: gang?.materialsResource >= 150,
                    image: rawMaterialsImg,
                },
                { name: "100 Life Essence", unlocked: gang?.essenceResource >= 100, image: lifeEssenceImg },
            ],
            unlocked: gang?.essenceResource >= 100 && gang?.materialsResource >= 150 && gang?.gang_member?.length >= 3,
        },
        {
            unlockText: "Unlocks Gang Bank + Gang MOTD",
            requirements: [
                {
                    name: "5 Gang Members",
                    unlocked: gang?.gang_member?.length >= 5,
                    image: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/UX78bPX.png`,
                },
                {
                    name: "400 Raw Materials",
                    unlocked: gang?.materialsResource >= 400,
                    image: rawMaterialsImg,
                },
                { name: "250 Life Essence", unlocked: gang?.essenceResource >= 250, image: lifeEssenceImg },
                {
                    name: "150 Tools",
                    unlocked: gang?.toolsResource >= 150,
                    image: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/d1vrEud.png`,
                },
            ],
            unlocked:
                gang?.toolsResource >= 150 &&
                gang?.essenceResource >= 250 &&
                gang?.materialsResource >= 400 &&
                gang?.gang_member?.length >= 5,
        },
        {
            unlockText: "Coming Soon...",
            requirements: [
                {
                    name: "7 Gang Members",
                    unlocked: gang?.gang_member?.length >= 5,
                    image: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/UX78bPX.png`,
                },
                {
                    name: "xxx Raw Materials",
                    unlocked: gang?.materialsResource >= 100,
                    image: rawMaterialsImg,
                },
                { name: "xxx Life Essence", unlocked: gang?.essenceResource >= 50, image: lifeEssenceImg },
            ],
            unlocked: false,
        },
    ];
    const nextUnlock = hideoutUnlocks[gang?.hideout_level ?? 0] ?? hideoutUnlocks[0];

    return (
        <div className="flex flex-col gap-2 overflow-y-auto p-2">
            <div className="flex flex-col text-center text-stroke-sm">
                <div className="mx-2 flex flex-col gap-2 overflow-y-auto p-1 md:mx-6">
                    <p className="mt-1 ml-4 text-left text-gray-200 text-sm uppercase leading-none md:text-center">
                        Resources
                    </p>{" "}
                    <div className="mx-auto w-full rounded-lg border-2 border-indigo-600 bg-slate-800 py-2 md:w-fit md:min-w-[50%] lg:px-14">
                        <div className="flex flex-col gap-1.5 px-3">
                            <ResourceDisplay
                                name="Respect"
                                amount={gang?.weeklyRespect}
                                src={respectImg}
                                textColor="text-red-400"
                            />
                            <hr className="my-0.5 border-gray-600" />
                            <ResourceDisplay
                                name="Raw Materials"
                                amount={gang?.materialsResource}
                                src={rawMaterialsImg}
                            />
                            <ResourceDisplay name="Life Essence" amount={gang?.essenceResource} src={lifeEssenceImg} />
                            <ResourceDisplay
                                name="Tools"
                                amount={gang?.toolsResource}
                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/d1vrEud.png`}
                            />
                            <ResourceDisplay
                                disabled
                                name="Tech Components"
                                amount={gang?.techResource}
                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/9Ye2F7T.png`}
                            />

                            <hr className="my-0.5 border-gray-600" />
                            <div className="flex gap-3 text-base text-custom-yellow text-stroke-md">
                                <div className="w-8">
                                    <img className="mx-auto h-6 w-auto" src={yenImg} alt="" />
                                </div>
                                <p className="my-auto">Gang Bank</p>
                                <p className="ml-auto text-right text-green-500 text-stroke-sm">
                                    {formatCurrency(gang?.treasury_balance || 0)}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="mx-auto mt-4 w-5/6 rounded-lg border-2 border-blue-500 bg-linear-to-b from-blue-950 to-blue-800 p-4 pt-2">
                    <div className="mr-5 flex h-12 items-center justify-center gap-2 text-lg">
                        <img
                            src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/nf7boKi.png`}
                            alt=""
                            className="h-6 w-auto"
                        />
                        <p>Hideout Level {gang?.hideout_level}</p>
                    </div>
                    <div className="mx-auto flex w-fit flex-col gap-1 divide-y divide-gray-600/50 rounded-lg bg-black/30 px-4 py-1">
                        {nextUnlock.requirements.map((requirement) => (
                            <div key={requirement.name} className="mx-auto flex w-full gap-2 text-base">
                                <HideoutRequirementDisplay requirement={requirement} />
                            </div>
                        ))}
                    </div>
                    <p className="mt-4 text-custom-yellow">Unlocks Chatroom + Gang Icon</p>
                    <p className="mb-1 text-green-400 text-sm">+3 Max Members</p>
                    <Button disabled={!nextUnlock.unlocked} className="mt-4! mx-auto w-1/2" onClick={upgradeHideout}>
                        {gang?.hideout_level === 0 ? "Construct Hideout" : "Upgrade Hideout"}
                    </Button>
                    {gang?.hideout_level === 0 && (
                        <p className="-mb-2.5 mt-2.5 flex w-full justify-center gap-2 text-center text-amber-500 text-sm">
                            <span className="-ml-3 my-auto text-amber-400">
                                {" "}
                                <Info />
                            </span>
                            The Chatroom takes 10 minutes to construct.
                        </p>
                    )}
                </div>
            </div>
        </div>
    );
};

const HideoutRequirementDisplay = ({ requirement }: { requirement: Requirement }) => {
    return (
        <div className={cn(requirement.unlocked ? "text-green-500" : "text-red-500", "m-auto flex items-center")}>
            <div className="mr-2 h-full w-8">
                <img src={requirement.image} alt="" className="m-auto h-5 w-auto" />
            </div>

            <div className="my-auto w-40 text-left">
                <p className="my-auto text-base">{requirement.name}</p>
            </div>
            <div className="flex-1">
                {requirement.unlocked ? <Check className="ml-2 inline size-4" /> : <X className="ml-2 inline size-4" />}
            </div>
        </div>
    );
};

const ResourceDisplay = ({ name, amount, src, textColor, maxAmount, disabled }: ResourceDisplayProps) => {
    return (
        <div
            className={cn(
                textColor ? textColor : "text-custom-yellow",
                "flex gap-3 text-base text-stroke-md",
                disabled && "text-gray-500! opacity-75"
            )}
        >
            <div className="w-8">
                <img className={cn(disabled && "grayscale", "mx-auto h-6 w-auto")} src={src} alt="" />
            </div>
            <p className="my-auto">{name}</p>
            <p
                className={cn(
                    textColor ? textColor : "text-blue-400",
                    "ml-auto text-right text-stroke-sm",
                    disabled && "text-gray-500!"
                )}
            >
                {amount}
                {maxAmount ? `/${maxAmount}` : ""}
            </p>
        </div>
    );
};

export default GangHideout;
