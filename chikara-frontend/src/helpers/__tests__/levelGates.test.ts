import { describe, expect, it, vi, beforeEach, afterEach } from "vitest";
import { checkLevelGate, levelGates } from "../levelGates";
import { persistStore } from "@/app/store/persistStore";

// Mock the persist store
vi.mock("@/app/store/persistStore", () => ({
    persistStore: {
        getState: vi.fn(),
    },
}));

describe("levelGates", () => {
    const mockGameConfig = {
        SHOP1_LEVEL_GATE: 5,
        SHOP2_LEVEL_GATE: 10,
        SHOP3_LEVEL_GATE: 15,
        JOBS_LEVEL_GATE: 3,
        CRAFTING_LEVEL_GATE: 8,
        TALENTS_LEVEL_GATE: 12,
        DAILY_QUESTS_LEVEL_GATE: 6,
        COURSES_LEVEL_GATE: 7,
        ROOFTOP_BATTLES_LEVEL_GATE: 20,
        MARKET_LEVEL_GATE: 4,
        ARCADE_LEVEL_GATE: 25,
    };

    beforeEach(() => {
        vi.mocked(persistStore.getState).mockReturnValue({
            gameConfig: mockGameConfig,
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe("checkLevelGate", () => {
        it("should return unlocked when user level meets requirement", () => {
            const result = checkLevelGate("job", 5);
            expect(result).toEqual({
                isLocked: false,
                requiredLevel: null,
                message: null,
                displayName: "Part-Time Job",
            });
        });

        it("should return locked when user level is below requirement", () => {
            const result = checkLevelGate("job", 2);
            expect(result).toEqual({
                isLocked: true,
                requiredLevel: 3,
                message: "Unlocked at level 3",
                displayName: "Part-Time Job",
            });
        });

        it("should handle unknown feature keys", () => {
            const result = checkLevelGate("unknown", 10);
            expect(result).toEqual({
                isLocked: false,
                requiredLevel: null,
                message: null,
            });
        });

        it("should handle missing game config", () => {
            vi.mocked(persistStore.getState).mockReturnValue({
                gameConfig: null,
            });

            const result = checkLevelGate("job", 5);
            expect(result).toEqual({
                isLocked: false,
                requiredLevel: null,
                message: null,
            });
        });

        it("should work for all configured features", () => {
            const testCases = [
                { key: "shop1", level: 4, expectedLocked: true, expectedLevel: 5 },
                { key: "shop2", level: 10, expectedLocked: false, expectedLevel: null },
                { key: "market", level: 3, expectedLocked: true, expectedLevel: 4 },
                { key: "arcade", level: 30, expectedLocked: false, expectedLevel: null },
            ];

            testCases.forEach(({ key, level, expectedLocked, expectedLevel }) => {
                const result = checkLevelGate(key, level);
                expect(result.isLocked).toBe(expectedLocked);
                expect(result.requiredLevel).toBe(expectedLevel);
            });
        });
    });

    describe("levelGates (legacy function)", () => {
        it("should handle shop gates correctly", () => {
            expect(levelGates("shop1", 4)).toBe(true); // locked
            expect(levelGates("shop1", 5)).toBe(null); // unlocked
            expect(levelGates("shop2", 9)).toBe(true); // locked
            expect(levelGates("shop2", 10)).toBe(null); // unlocked
        });

        it("should handle redirect types", () => {
            expect(levelGates("jobRedirect", 2)).toBe(true); // should redirect
            expect(levelGates("jobRedirect", 3)).toBe(false); // no redirect
            expect(levelGates("marketRedirect", 3)).toBe(true); // should redirect
            expect(levelGates("marketRedirect", 4)).toBe(false); // no redirect
        });

        it("should handle LVL types", () => {
            expect(levelGates("jobLVL", 2)).toBe(3); // return required level
            expect(levelGates("jobLVL", 3)).toBe(false); // unlocked
            expect(levelGates("arcadeLVL", 20)).toBe(25); // return required level
            expect(levelGates("arcadeLVL", 25)).toBe(false); // unlocked
        });

        it("should handle message types", () => {
            expect(levelGates("job", 2)).toBe("Part-Time Job - Requires level 3");
            expect(levelGates("job", 3)).toBe(false);
            expect(levelGates("market", 3)).toBe("Market - Requires level 4");
            expect(levelGates("market", 4)).toBe(false);
        });

        it("should handle level-only types", () => {
            expect(levelGates("talents", 10)).toBe(12); // return required level
            expect(levelGates("talents", 12)).toBe(false); // unlocked
            expect(levelGates("dailyQuests", 5)).toBe(6); // return required level
            expect(levelGates("dailyQuests", 6)).toBe(false); // unlocked
        });

        it("should return null for unknown types", () => {
            expect(levelGates("unknown", 10)).toBe(null);
            expect(levelGates("", 10)).toBe(null);
        });

        it("should handle missing game config", () => {
            vi.mocked(persistStore.getState).mockReturnValue({
                gameConfig: null,
            });

            expect(levelGates("job", 5)).toBe(null);
            expect(levelGates("shop1", 5)).toBe(null);
        });
    });
});
