import { describe, expect, it, vi, beforeEach } from "vitest";
import { getItemStatWithUpgrades, getItemFrame, getItemTypeIconImage, getItemType } from "../itemHelpers";
import type { InventoryItem, Item } from "@/types/item";
import { ItemTypes } from "@/types/item";

// Note: addUpgradeModifiers is defined locally in itemHelpers.ts

// Mock environment variables
vi.mock("import.meta", () => ({
    env: {
        VITE_IMAGE_CDN_URL: "https://test-cdn.com",
    },
}));

describe("itemHelpers", () => {
    beforeEach(() => {
        // Mock the environment variable for each test
        vi.stubEnv("VITE_IMAGE_CDN_URL", "https://test-cdn.com");
    });
    describe("getItemStatWithUpgrades", () => {
        it("should return base stat when no upgrades", () => {
            const item: InventoryItem = {
                id: 1,
                userId: 1,
                itemId: 1,
                upgradeLevel: 0,
                quality: "normal",
                item: {
                    id: 1,
                    name: "Test Item",
                    damage: 100,
                    armour: 50,
                } as Item,
            };

            expect(getItemStatWithUpgrades(item, "damage")).toBe(100);
            expect(getItemStatWithUpgrades(item, "armour")).toBe(50);
        });

        it("should apply upgrade modifiers correctly", () => {
            const item: InventoryItem = {
                id: 1,
                userId: 1,
                itemId: 1,
                upgradeLevel: 2,
                quality: "normal",
                item: {
                    id: 1,
                    name: "Test Item",
                    damage: 100,
                } as Item,
            };

            // With 2 upgrade levels, should be 100 * 1.1 = 110 (5% per level)
            expect(getItemStatWithUpgrades(item, "damage")).toBe(110);
        });

        it("should handle missing item or stat", () => {
            const item: InventoryItem = {
                id: 1,
                userId: 1,
                itemId: 1,
                upgradeLevel: 1,
                quality: "normal",
                item: null as any,
            };

            expect(getItemStatWithUpgrades(item, "damage")).toBe(0);
        });

        it("should handle missing upgrade level", () => {
            const item: InventoryItem = {
                id: 1,
                userId: 1,
                itemId: 1,
                upgradeLevel: undefined as any,
                quality: "normal",
                item: {
                    id: 1,
                    name: "Test Item",
                    damage: 100,
                } as Item,
            };

            expect(getItemStatWithUpgrades(item, "damage")).toBe(100);
        });
    });

    describe("getItemFrame", () => {
        it("should return correct frame for each rarity", () => {
            const testCases = [
                { rarity: "novice", itemType: "weapon" },
                { rarity: "standard", itemType: "weapon" },
                { rarity: "enhanced", itemType: "weapon" },
                { rarity: "specialist", itemType: "weapon" },
                { rarity: "military", itemType: "weapon" },
                { rarity: "legendary", itemType: "weapon" },
            ];

            testCases.forEach(({ rarity, itemType }) => {
                const item = { rarity, itemType } as Item;
                const frame = getItemFrame(item);
                expect(frame).toBeDefined();
                expect(typeof frame).toBe("string");
            });
        });

        it("should return special frame for special items", () => {
            const item = { rarity: "standard", itemType: "special" } as Item;
            const frame = getItemFrame(item);
            expect(frame).toBeDefined();
        });

        it("should return default frame for unknown rarity", () => {
            const item = { rarity: "unknown", itemType: "weapon" } as Item;
            const frame = getItemFrame(item);
            expect(frame).toBeDefined();
        });

        it("should handle null item", () => {
            const frame = getItemFrame(null as any);
            expect(frame).toBeDefined();
        });
    });

    describe("getItemTypeIconImage", () => {
        it("should return correct icon URL for each item type", () => {
            const itemTypes: ItemTypes[] = [
                "weapon",
                "ranged",
                "head",
                "chest",
                "hands",
                "legs",
                "feet",
                "finger",
                "offhand",
                "shield",
                "recipe",
                "upgrade",
                "pet",
                "pet_food",
            ];

            itemTypes.forEach((itemType) => {
                const item = { itemType } as Item;
                const icon = getItemTypeIconImage(item);
                expect(icon).toBeDefined();
                expect(typeof icon).toBe("string");
                expect(icon).toContain("https://test-cdn.com");
            });
        });

        it("should return default icon for unknown item type", () => {
            const item = { itemType: "unknown" } as any;
            const icon = getItemTypeIconImage(item);
            expect(icon).toContain("https://test-cdn.com");
        });

        it("should handle null item", () => {
            const icon = getItemTypeIconImage(null as any);
            expect(icon).toContain("https://test-cdn.com");
        });
    });

    describe("getItemType", () => {
        it("should return proper display names for item types", () => {
            expect(getItemType("hands")).toBe("Gloves");
            expect(getItemType("finger")).toBe("Ring");
            expect(getItemType("weapon")).toBe("Weapon");
            expect(getItemType("chest")).toBe("Chest");
            expect(getItemType("head")).toBe("Head");
        });

        it("should capitalize first letter for standard types", () => {
            expect(getItemType("weapon")).toBe("Weapon");
            expect(getItemType("shield")).toBe("Shield");
            expect(getItemType("offhand")).toBe("Offhand");
        });

        it("should handle special cases", () => {
            expect(getItemType("hands")).toBe("Gloves");
            expect(getItemType("finger")).toBe("Ring");
        });
    });
});
