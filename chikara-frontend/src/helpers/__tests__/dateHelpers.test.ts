import { describe, expect, it, vi, beforeEach } from "vitest";
import {
    getNow,
    getNextMidnightDate,
    calculateTimeUntilMidnight,
    getNextSundayMidnight,
    getNext6PMDate,
    timeRemaining,
    formatTimeUntilExpiration,
    isExpired,
    formatTimeToNow,
} from "../dateHelpers";

// Mock the timestamp helpers
vi.mock("@/utils/timestampHelpers", () => ({
    safeTimestampToDate: vi.fn((timestamp) => {
        if (timestamp === null || timestamp === undefined) return null;
        if (typeof timestamp === "string" && timestamp === "invalid") return null;
        return new Date(Number(timestamp));
    }),
}));

describe("dateHelpers", () => {
    beforeEach(() => {
        // Use the mocked time from setup.ts (2024-01-15T12:00:00Z)
        vi.setSystemTime(new Date("2024-01-15T12:00:00Z"));
    });

    describe("getNow", () => {
        it("should return current UTC date", () => {
            const now = getNow();
            expect(now.constructor.name).toBe("UTCDate");
            expect(now.getTime()).toBe(Date.now());
        });
    });

    describe("getNextMidnightDate", () => {
        it("should return tomorrow at midnight UTC", () => {
            const nextMidnight = getNextMidnightDate();
            expect(nextMidnight.getUTCHours()).toBe(0);
            expect(nextMidnight.getUTCMinutes()).toBe(0);
            expect(nextMidnight.getUTCSeconds()).toBe(0);
            expect(nextMidnight.getUTCMilliseconds()).toBe(0);
            expect(nextMidnight.getUTCDate()).toBe(16); // Tomorrow
        });
    });

    describe("calculateTimeUntilMidnight", () => {
        it("should calculate time until next midnight", () => {
            // Current time is 12:00:00 UTC, so 12 hours until midnight
            const result = calculateTimeUntilMidnight();
            expect(result).toBe("12h 0m");
        });

        it("should handle different times correctly", () => {
            // Test at 23:30 UTC (30 minutes until midnight)
            vi.setSystemTime(new Date("2024-01-15T23:30:00Z"));
            const result = calculateTimeUntilMidnight();
            expect(result).toBe("0h 30m");
        });
    });

    describe("getNextSundayMidnight", () => {
        it("should return next Sunday midnight timestamp", () => {
            const nextSunday = getNextSundayMidnight();
            const date = new Date(nextSunday);
            expect(date.getUTCDay()).toBe(0); // Sunday
            expect(date.getUTCHours()).toBe(0);
            expect(date.getUTCMinutes()).toBe(0);
        });
    });

    describe("getNext6PMDate", () => {
        it("should return today at 6PM if current time is before 6PM", () => {
            // Current time is 12:00 UTC, so next 6PM is today
            const next6PM = getNext6PMDate();
            const date = new Date(next6PM);
            expect(date.getUTCHours()).toBe(18);
            expect(date.getUTCDate()).toBe(15); // Today
        });

        it("should return tomorrow at 6PM if current time is after 6PM", () => {
            vi.setSystemTime(new Date("2024-01-15T19:00:00Z")); // 7PM UTC
            const next6PM = getNext6PMDate();
            const date = new Date(next6PM);
            expect(date.getUTCHours()).toBe(18);
            expect(date.getUTCDate()).toBe(16); // Tomorrow
        });
    });

    describe("timeRemaining", () => {
        it("should format time remaining correctly", () => {
            const futureTime = Date.now() + 3600000; // 1 hour from now
            const result = timeRemaining(futureTime);
            expect(result).toBe("1 hour");
        });

        it("should return null for falsy input", () => {
            expect(timeRemaining(0)).toBe(null);
            expect(timeRemaining(null as any)).toBe(null);
        });
    });

    describe("formatTimeUntilExpiration", () => {
        it("should return 'Expired' for past dates", () => {
            const pastDate = new Date(Date.now() - 1000);
            expect(formatTimeUntilExpiration(pastDate)).toBe("Expired");
        });

        it("should format minutes correctly", () => {
            const futureDate = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
            expect(formatTimeUntilExpiration(futureDate)).toBe("5m");
        });

        it("should format hours correctly", () => {
            const futureDate = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours
            expect(formatTimeUntilExpiration(futureDate)).toBe("2h");
        });

        it("should format days correctly", () => {
            const futureDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000); // 3 days
            expect(formatTimeUntilExpiration(futureDate)).toBe("3d");
        });

        it("should handle less than 1 minute", () => {
            const futureDate = new Date(Date.now() + 30 * 1000); // 30 seconds
            expect(formatTimeUntilExpiration(futureDate)).toBe("< 1m");
        });
    });

    describe("isExpired", () => {
        it("should return true for past dates", () => {
            const pastDate = new Date(Date.now() - 1000);
            expect(isExpired(pastDate)).toBe(true);
        });

        it("should return false for future dates", () => {
            const futureDate = new Date(Date.now() + 1000);
            expect(isExpired(futureDate)).toBe(false);
        });

        it("should return true for current time", () => {
            const now = new Date();
            expect(isExpired(now)).toBe(true);
        });
    });

    describe("formatTimeToNow", () => {
        it("should format time to now for valid dates", () => {
            const pastDate = new Date(Date.now() - 3600000); // 1 hour ago
            const result = formatTimeToNow(pastDate);
            expect(result).toBe("1 hour");
        });

        it("should handle timestamp inputs", () => {
            const timestamp = Date.now() - 3600000; // 1 hour ago
            const result = formatTimeToNow(timestamp);
            expect(result).toBe("1 hour");
        });

        it("should return null for invalid inputs", () => {
            expect(formatTimeToNow(null)).toBe(null);
            expect(formatTimeToNow(undefined)).toBe(null);
            expect(formatTimeToNow("invalid")).toBe(null);
        });
    });
});
