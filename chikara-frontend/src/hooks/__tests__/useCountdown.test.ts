import { describe, expect, it, vi, beforeEach, afterEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useCountdown, getReturnValues } from "../useCountdown";

describe("useCountdown", () => {
    beforeEach(() => {
        vi.useRealTimers();
        vi.useFakeTimers();
        vi.setSystemTime(new Date("2024-01-15T12:00:00Z"));
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    describe("getReturnValues", () => {
        it("should calculate time values correctly for positive countdown", () => {
            const oneDay = 24 * 60 * 60 * 1000;
            const oneHour = 60 * 60 * 1000;
            const oneMinute = 60 * 1000;
            const oneSecond = 1000;

            // 1 day, 2 hours, 3 minutes, 4 seconds
            const countDown = oneDay + 2 * oneHour + 3 * oneMinute + 4 * oneSecond;
            const [days, hours, minutes, seconds] = getReturnValues(countDown);

            expect(days).toBe(1);
            expect(hours).toBe("02");
            expect(minutes).toBe("03");
            expect(seconds).toBe("04");
        });

        it("should pad single digits with zeros", () => {
            const countDown = 5 * 60 * 1000 + 7 * 1000; // 5 minutes, 7 seconds
            const [days, hours, minutes, seconds] = getReturnValues(countDown);

            expect(days).toBe(0);
            expect(hours).toBe("00");
            expect(minutes).toBe("05");
            expect(seconds).toBe("07");
        });

        it("should handle zero countdown", () => {
            const [days, hours, minutes, seconds] = getReturnValues(0);

            expect(days).toBe(0);
            expect(hours).toBe("00");
            expect(minutes).toBe("00");
            expect(seconds).toBe("00");
        });

        it("should handle negative countdown", () => {
            const [days, hours, minutes, seconds] = getReturnValues(-1000);

            expect(days).toBe(0);
            expect(hours).toBe("00");
            expect(minutes).toBe("00");
            expect(seconds).toBe("00");
        });

        it("should handle large time values", () => {
            const countDown = 10 * 24 * 60 * 60 * 1000 + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000;
            const [days, hours, minutes, seconds] = getReturnValues(countDown);

            expect(days).toBe(10);
            expect(hours).toBe("23");
            expect(minutes).toBe("59");
            expect(seconds).toBe("59");
        });
    });

    describe("useCountdown hook", () => {
        it("should initialize with correct countdown value", () => {
            const targetDate = "2024-01-15T13:00:00Z"; // 1 hour from now
            const { result } = renderHook(() => useCountdown(targetDate));

            const [days, hours, minutes, seconds] = result.current;
            expect(days).toBe(0);
            expect(hours).toBe("01");
            expect(minutes).toBe("00");
            expect(seconds).toBe("00");
        });

        it("should update countdown every second", () => {
            const targetDate = "2024-01-15T12:01:00Z"; // 1 minute from now
            const { result } = renderHook(() => useCountdown(targetDate));

            // Initial state
            let [days, hours, minutes, seconds] = result.current;
            expect(minutes).toBe("01");
            expect(seconds).toBe("00");

            // Advance time by 1 second
            act(() => {
                vi.advanceTimersByTime(1000);
            });

            [days, hours, minutes, seconds] = result.current;
            expect(minutes).toBe("00");
            expect(seconds).toBe("59");
        });

        it("should handle countdown reaching zero", () => {
            const targetDate = "2024-01-15T12:00:02Z"; // 2 seconds from now
            const { result } = renderHook(() => useCountdown(targetDate));

            // Initial state
            let [days, hours, minutes, seconds] = result.current;
            expect(seconds).toBe("02");

            // Advance time by 3 seconds (past the target)
            act(() => {
                vi.advanceTimersByTime(3000);
            });

            [days, hours, minutes, seconds] = result.current;
            expect(days).toBe(0);
            expect(hours).toBe("00");
            expect(minutes).toBe("00");
            expect(seconds).toBe("00");
        });

        it("should handle past target dates", () => {
            const targetDate = "2024-01-15T11:00:00Z"; // 1 hour ago
            const { result } = renderHook(() => useCountdown(targetDate));

            const [days, hours, minutes, seconds] = result.current;
            expect(days).toBe(0);
            expect(hours).toBe("00");
            expect(minutes).toBe("00");
            expect(seconds).toBe("00");
        });

        it("should update when target date changes", () => {
            let targetDate = "2024-01-15T13:00:00Z"; // 1 hour from now
            const { result, rerender } = renderHook(({ date }) => useCountdown(date), {
                initialProps: { date: targetDate },
            });

            // Initial state - 1 hour
            let [days, hours, minutes, seconds] = result.current;
            expect(hours).toBe("01");

            // Change target to 2 hours from now
            targetDate = "2024-01-15T14:00:00Z";
            rerender({ date: targetDate });

            [days, hours, minutes, seconds] = result.current;
            expect(hours).toBe("02");
        });

        it("should clean up interval on unmount", () => {
            const targetDate = "2024-01-15T13:00:00Z";
            const { unmount } = renderHook(() => useCountdown(targetDate));

            const clearIntervalSpy = vi.spyOn(global, "clearInterval");
            unmount();

            expect(clearIntervalSpy).toHaveBeenCalled();
        });
    });
});
