import { describe, it, expect, vi } from "vitest";
import { render } from "@testing-library/react";
import { mockFunctions } from "./test/setup";

// Import mocked modules - these are now globally mocked in setup.ts
import ReactDOM from "react-dom/client";
import { initializeAnalytics } from "./lib/analytics";
import { createQueryClient } from "./lib/queryClient";

describe("index.tsx", () => {
    beforeEach(() => {
        // Clear module cache to ensure fresh imports
        vi.resetModules();
    });

    it("sets up vite preload error handler", async () => {
        // Import the module to trigger the setup
        await import("./index");

        expect(mockFunctions.addEventListener).toHaveBeenCalledWith("vite:preloadError", expect.any(Function));
    });

    it("calls window.location.reload when vite:preloadError occurs", async () => {
        await import("./index");

        // Get the event handler that was registered
        const eventHandler = mockFunctions.addEventListener.mock.calls.find(
            (call) => call[0] === "vite:preloadError"
        )?.[1];

        expect(eventHandler).toBeDefined();

        // Simulate the event
        if (eventHandler) {
            eventHandler();
            expect(mockFunctions.reload).toHaveBeenCalled();
        }
    });

    it("initializes analytics", async () => {
        await import("./index");

        expect(initializeAnalytics).toHaveBeenCalled();
    });

    it("creates query client", async () => {
        await import("./index");

        expect(createQueryClient).toHaveBeenCalled();
    });

    it("creates React root and renders app", async () => {
        const mockRender = vi.fn();
        const mockCreateRoot = vi.fn(() => ({
            render: mockRender,
        }));

        vi.mocked(ReactDOM.createRoot).mockImplementation(mockCreateRoot);

        await import("./index");

        expect(mockFunctions.getElementById).toHaveBeenCalledWith("root");
        expect(mockCreateRoot).toHaveBeenCalledWith({ id: "root" });
        expect(mockRender).toHaveBeenCalled();
    });

    it("renders the correct component structure", () => {
        // Test the JSX structure by rendering it directly
        const TestComponent = () => (
            <div>
                <div data-testid="query-client-provider">
                    <div data-testid="error-boundary">
                        <div data-testid="app-router">App Router</div>
                    </div>
                    <div className="hidden md:block">
                        <div data-testid="react-query-devtools">React Query Devtools</div>
                    </div>
                </div>
            </div>
        );

        const { getByTestId } = render(<TestComponent />);

        expect(getByTestId("query-client-provider")).toBeInTheDocument();
        expect(getByTestId("error-boundary")).toBeInTheDocument();
        expect(getByTestId("app-router")).toBeInTheDocument();
        expect(getByTestId("react-query-devtools")).toBeInTheDocument();
    });

    it("conditionally renders ServiceWorkerUpdater in non-development mode", () => {
        // Mock production mode
        vi.mocked(import.meta).env.MODE = "production";

        const TestComponent = () => (
            <div data-testid="error-boundary">
                {import.meta.env.MODE !== "development" && (
                    <div data-testid="service-worker-updater">Service Worker Updater</div>
                )}
                <div data-testid="app-router">App Router</div>
            </div>
        );

        const { getByTestId } = render(<TestComponent />);

        expect(getByTestId("service-worker-updater")).toBeInTheDocument();
    });

    it("does not render ServiceWorkerUpdater in development mode", () => {
        // Mock development mode
        vi.mocked(import.meta).env.MODE = "development";

        const TestComponent = () => (
            <div data-testid="error-boundary">
                {import.meta.env.MODE !== "development" && (
                    <div data-testid="service-worker-updater">Service Worker Updater</div>
                )}
                <div data-testid="app-router">App Router</div>
            </div>
        );

        const { getByTestId, queryByTestId } = render(<TestComponent />);

        expect(getByTestId("app-router")).toBeInTheDocument();
        expect(queryByTestId("service-worker-updater")).not.toBeInTheDocument();
    });

    it("renders ReactQueryDevtools with correct props", () => {
        const TestComponent = () => (
            <div className="hidden md:block">
                <div
                    data-testid="react-query-devtools"
                    data-initial-is-open="false"
                    data-button-position="top-left"
                    data-position="right"
                >
                    React Query Devtools
                </div>
            </div>
        );

        const { getByTestId } = render(<TestComponent />);
        const devtools = getByTestId("react-query-devtools");

        expect(devtools).toBeInTheDocument();
        expect(devtools).toHaveAttribute("data-initial-is-open", "false");
        expect(devtools).toHaveAttribute("data-button-position", "top-left");
        expect(devtools).toHaveAttribute("data-position", "right");
    });
});
