/// <reference types="vitest/config" />
import path from "path";
import { sentryVitePlugin } from "@sentry/vite-plugin";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";
import { VitePWA } from "vite-plugin-pwa";
import packageJson from "./package.json";

const ReactCompilerConfig = {
    sources: (filename: string) => {
        return filename.indexOf("src/") !== -1 && !filename.endsWith("App.tsx") && !filename.includes("/test/");
    },
};
// const ReactCompilerConfig = {};

export default ({ mode }: { mode: string }) => {
    const isDevelopment = mode === "development";
    return defineConfig({
        server: {
            port: 8080,
        },
        build: {
            sourcemap: false, // Source maps on for Sentry
        },
        plugins: [
            tailwindcss(),
            react({
                babel: {
                    plugins: [["babel-plugin-react-compiler", ReactCompilerConfig]],
                },
            }),
            VitePWA({
                strategies: "injectManifest",
                injectManifest: {
                    globPatterns: ["**/*"],
                    maximumFileSizeToCacheInBytes: 3145728, // 3 MiB
                },
                registerType: "autoUpdate",
                srcDir: "./service-worker",
                filename: "firebase-messaging-sw.js",
                workbox: {
                    maximumFileSizeToCacheInBytes: 3145728, // 3 MiB
                },
                manifest: {
                    name: "Chikara Battle Academy",
                    short_name: "Chikara Academy",
                    description: "Chikara Battle Academy MMORPG",
                    theme_color: "#07072e",
                    background_color: "#07072e",
                    start_url: "/",
                    icons: [
                        {
                            src: "./logo512.png",
                            sizes: "512x512",
                            type: "image/png",
                        },
                        {
                            src: "./logo192.png",
                            sizes: "192x192",
                            type: "image/png",
                            purpose: "maskable",
                        },
                    ],
                    orientation: "portrait",
                    display: "standalone",
                    id: "chikaraacademy",
                    categories: ["games"],
                },
            }),
            // sentryVitePlugin({
            //     org: "clearwater-games",
            //     project: "javascript-react",
            //     disable: isDevelopment,
            //     authToken: "490432d509284b86a95c8a784fca9d68217853ce2baa4710b12220d25097ccae",
            //     sourcemaps: {
            //         // Specify the directory containing build artifacts
            //         assets: "./dist/**",
            //         // Don't upload the source maps of dependencies
            //         ignore: ["./node_modules/**"],
            //     },

            //     // Helps troubleshooting - set to false to make plugin less noisy
            //     debug: false,

            //     // Optionally uncomment the line below to override automatic release name detection
            //     // release: env.RELEASE,
            // }),
        ],
        define: {
            "process.env.NODE_ENV": `"${mode}"`,
            "import.meta.env.VITE_PACKAGE_VERSION": JSON.stringify(packageJson.version),
        },
        resolve: {
            alias: {
                "@": path.resolve(__dirname, "./src"),
            },
        },
        test: {
            globals: true,
            environment: "jsdom",
            setupFiles: ["./src/test/setup.ts"],
            css: true,
            include: ["src/**/*.{test,spec}.{ts,mts,tsx}"],
            exclude: ["**/node_modules/**", "**/dist/**", "**/.{idea,git,cache,output,temp}/**"],
            clearMocks: true,
            restoreMocks: true,
            mockReset: true,
            isolate: true,
        },
    });
};
