# Crafting Recipe Creation Guide

---

## Overview

Crafting recipes in Chikara Academy enable players to create items using gathered resources and materials. This guide provides the structure and details required to create balanced and engaging crafting recipes that integrate seamlessly with the game's progression systems.

---

## Recipe Structure

Each crafting recipe is defined with the following core attributes:

- **ID**: Unique identifier for the recipe
- **Cost**: Yen cost required to craft the item (beyond material costs)
- **CraftTime**: Time in seconds required to complete the craft
- **IsUnlockable**: Whether the recipe must be learned/unlocked before use
- **RequiredSkillType**: Specific crafting skill required (fabrication, electronics, chemistry, outfitting)
- **RequiredSkillLevel**: Minimum skill level needed to craft this recipe

---

## Crafting Skills

The game features four crafting skill categories:

### Fabrication
- **Purpose**: Creating weapons, tools, and basic equipment
- **Materials**: Metals, plastics, basic components
- **Example Items**: Melee weapons, simple tools, basic armor pieces

### Electronics
- **Purpose**: Advanced technological items and gadgets
- **Materials**: Circuits, wires, electronic components
- **Example Items**: Ranged weapons, tech gadgets, enhancement modules

### Chemistry
- **Purpose**: Consumables, buffs, and chemical compounds
- **Materials**: Chemicals, organic compounds, catalysts
- **Example Items**: Healing potions, temporary buffs, explosives

### Outfitting
- **Purpose**: Clothing, armor, and wearable items
- **Materials**: Fabrics, leather, protective materials
- **Example Items**: Armor sets, clothing, protective gear

---

## Recipe Components

### Input Items (recipe_item with itemType: "input")
- **Count**: Quantity of the item required
- **ItemId**: Reference to the specific item needed
- **ItemType**: Set to "input" for materials consumed

### Output Items (recipe_item with itemType: "output")
- **Count**: Quantity of items produced
- **ItemId**: Reference to the item created
- **ItemType**: Set to "output" for items produced

---

## Recipe Balancing Guidelines

### Cost Calculation
1. **Base Cost**: Yen cost should reflect item value and complexity
2. **Material Cost**: Sum of all input item values
3. **Skill Investment**: Higher skill requirements justify better rewards
4. **Time Investment**: Longer craft times should yield better results

### Skill Requirements
- **Levels 1-25**: Basic items, common materials
- **Levels 26-50**: Intermediate items, mixed materials
- **Levels 51-75**: Advanced items, rare components
- **Levels 76-100**: Master-tier items, exotic materials

### Craft Time Guidelines
- **Basic Items**: 30-300 seconds (0.5-5 minutes)
- **Intermediate Items**: 300-1800 seconds (5-30 minutes)
- **Advanced Items**: 1800-7200 seconds (30 minutes-2 hours)
- **Master Items**: 7200+ seconds (2+ hours)

---

## Recipe Categories

### Consumables
- **Healing Items**: Restore health, cure status effects
- **Buff Items**: Temporary stat boosts, special abilities
- **Utility Items**: Tools for specific actions or encounters

### Equipment
- **Weapons**: Melee and ranged combat items
- **Armor**: Protective gear for all equipment slots
- **Accessories**: Rings, off-hand items, special equipment

### Crafting Materials
- **Refined Materials**: Processed raw resources
- **Components**: Intermediate crafting ingredients
- **Upgrade Materials**: Items used to enhance existing equipment

---

## Recipe Relations

### Unlocking Mechanisms
- **Item Unlock**: Some items unlock recipes when obtained
- **Quest Rewards**: Recipes granted through quest completion
- **Skill Milestones**: Recipes unlocked at specific skill levels
- **Discovery**: Found through exploration or special events

### Dependencies
- **Prerequisite Items**: Items that must exist before recipe can be used
- **Skill Progression**: Natural progression through crafting skill trees
- **Story Progression**: Recipes tied to narrative advancement

---

## JSON Structure Example

```json
{
  "id": 1,
  "cost": 50,
  "craftTime": 300,
  "isUnlockable": true,
  "requiredSkillType": "fabrication",
  "requiredSkillLevel": 15,
  "recipe_items": [
    {
      "itemId": 101,
      "count": 2,
      "itemType": "input"
    },
    {
      "itemId": 102,
      "count": 1,
      "itemType": "input"
    },
    {
      "itemId": 201,
      "count": 1,
      "itemType": "output"
    }
  ]
}
```

---

## Creation Guidelines

### Balance Considerations
1. **Input-Output Ratio**: Ensure fair exchange between materials and results
2. **Skill Progression**: Recipes should encourage skill development
3. **Economic Impact**: Consider market effects of craftable items
4. **Player Engagement**: Balance convenience with meaningful choices

### Quality Assurance
1. **Material Availability**: Ensure input materials are obtainable
2. **Progression Logic**: Recipes should unlock at appropriate times
3. **Value Proposition**: Crafted items should be worthwhile
4. **Skill Synergy**: Recipes should complement other crafting paths

### Naming Conventions
- **Recipe Names**: Should clearly indicate the output item
- **Descriptions**: Brief explanation of the crafting process
- **Categories**: Consistent grouping for UI organization

---

## Integration with Game Systems

### User Progression
- **Skill Experience**: Crafting grants experience in relevant skills
- **Achievement Integration**: Recipes can trigger achievement progress
- **Quest Objectives**: Crafting specific items for quest completion

### Economic Systems
- **Market Impact**: Craftable items affect marketplace dynamics
- **Resource Demand**: Recipes create demand for gathering activities
- **Currency Flow**: Crafting costs provide currency sinks

### Social Features
- **Gang Crafting**: Recipes that benefit from gang resources
- **Shared Knowledge**: Recipes that can be taught between players
- **Collaborative Projects**: Multi-player crafting opportunities

---

## Advanced Features

### Quality Variations
- **Skill-Based Quality**: Higher skill levels improve output quality
- **Material Quality**: Better input materials yield better results
- **Random Elements**: Chance-based quality improvements

### Batch Crafting
- **Quantity Multipliers**: Crafting multiple items simultaneously
- **Efficiency Bonuses**: Reduced per-item costs for bulk crafting
- **Time Optimization**: Batch processing for common items

### Specialization Paths
- **Master Recipes**: Exclusive high-level crafting options
- **Signature Items**: Unique recipes for dedicated crafters
- **Innovation System**: Player-driven recipe discovery

---

This guide provides the foundation for creating engaging and balanced crafting recipes that enhance the Chikara Academy gameplay experience.
