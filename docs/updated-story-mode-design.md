# Story Mode Design Document (V2) - Chikara Academy PBBG

## Overview

### Vision

To deliver a compelling, long-term narrative for Chikara Academy by deeply weaving story content into the core gameplay loop. The Story Mode will be experienced through a specialized "Main Story" quest line, making the Quest Log the player's central hub for all progression. This eliminates redundant systems and provides a clear, intuitive path for players to follow, creating a "living story" that feels like an integral part of the world, not a separate feature.

### Key Principles

- **Quest-Driven Narrative**: The primary method for advancing the story is by completing Main Story Quests found in the player's Quest Log.

- **Single Source of Truth**: The Quest Log is the player's definitive guide on what to do next for all major activities, including the story.

- **Seamless Integration**: Story beats (dialogue, choices, scenes) are presented as objectives within quests, seamlessly blending narrative with gameplay tasks (combat, exploration, crafting).

- **Time-Gated Chapters**: New story chapters (consisting of a new set of Story Quests) will be released on a schedule, creating an episodic, TV-series-like experience.

### Benefits of the New Design

- **Intuitive Player Experience**: Players only need to check their Quest Log to understand their next steps, removing the confusion of juggling a separate story progress tracker.

- **Simplified Development**: Eliminates redundant progression logic, brittle integration points (triggersQuestId), and complex state management.

- **Enhanced Content Flexibility**: Designers can easily mix narrative objectives (Complete Episode X) with standard gameplay objectives (Defeat 5 rivals) within a single quest, creating more dynamic and engaging content.

- **Robust & Maintainable**: The system is built on the core questing engine, making it more stable and easier to debug and expand in the future.

## Core Architecture

The story is structured hierarchically, with the Quest system serving as the fundamental engine for progression.

```
Story Season (e.g., "Academy Foundations", 6-12 months)
└── Story Chapter (e.g., "Arrival", 2-4 weeks)
    └── Story Quest (Player-facing quest in the log)
        ├── Quest Objective 1 (e.g., Go to location)
        │   └── Triggers Story Episode #1 (Narrative Scene)
        ├── Quest Objective 2 (e.g., Win 3 battles)
        └── Quest Objective 3 (e.g., Make a choice)
            └── Triggers Story Episode #2 (Choice Scene)
```

### Quest Auto-Starting Behavior

Currently the quest system requires the player to manually 'start' each quest. For the story mode every quest should auto-start except for the first quest in each chapter. This ensures seamless progression through the narrative while still requiring player engagement to begin each new chapter.

### Player Flow Example: Chapter 1, "Arrival"

1. **Chapter Unlock**: On the scheduled date, Chapter 1 becomes available to all players meeting the level requirement (e.g., Level 5).

2. **Quest Appears**: The first Story Quest of the chapter, "First Day at the Academy," is automatically added to the player's Quest Log, marked with a special "Main Story" icon. Note: This first quest requires manual starting by the player.

3. **Player Checks Quest Log**: The log shows the active objective:
    - **Objective 1**: Go to the Academy Gates.

4. **Gameplay Interaction**: The player navigates to the Explore map. The "Academy Gates" location now has a "!" icon, indicating a quest objective.

5. **Episode Triggered**: Clicking the node at the Academy Gates initiates Story Episode #1 ("The Welcoming Committee"), a self-contained dialogue scene.

6. **Objective Completion**: Upon finishing the dialogue, the COMPLETE_EPISODE objective is marked as complete.

7. **Quest Log Updates**: The Quest Log now displays the next objective for the same quest:
    - **Objective 2**: Prove your strength in 3 training battles.

8. **Mixed Gameplay**: The player completes this standard combat objective.

9. **Final Objective**: The log updates again:
    - **Objective 3**: Meet your new roommate.

10. **Quest Completion**: After completing the final objective (which may trigger another episode), the quest "First Day at the Academy" is marked as complete. The player receives rewards, and the next Story Quest in the chapter is automatically unlocked and auto-started (unlike the first quest of each chapter which requires manual starting).

## Revised Database Schema

This schema simplifies the design by making quest the central driver and story_episode a content model.

```prisma
// ===============================================
// 1. Story & Quest Structure
// ===============================================

model story_season {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  description String?  @db.Text
  startDate   DateTime @db.DateTime(0)

  requiredLevel Int @default(1) @db.UnsignedInt

  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)

  story_chapter       story_chapter[]
}

model story_chapter {
  id          Int     @id @default(autoincrement())
  seasonId    Int
  name        String  @db.VarChar(255)
  description String? @db.Text
  order       Int

  unlockDate DateTime @db.DateTime(0)

  requiredLevel Int @default(1) @db.UnsignedInt

  /// [RequiredChapterIds]
  requiredChapterIds Json?

  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)

  story_season        story_season          @relation(fields: [seasonId], references: [id], onDelete: Cascade)
  quests       quest[]      // A chapter is now a collection of quests

  @@unique([seasonId, order])
  @@index([seasonId])
  @@index([unlockDate])
}

// MODIFIED: quest is now the central progression driver
model quest {
  id                Int      @id @default(autoincrement())
  name              String   @db.VarChar(255)
  description       String?  @db.VarChar(255)
  questInfo         String?  @db.VarChar(255)
  levelReq          Int      @default(0) @db.UnsignedInt
  disabled          Boolean? @default(false)
  questChainName    String?  @db.VarChar(255)
  xpReward          Int?     @default(1) @db.UnsignedInt
  cashReward        Int      @default(0) @db.UnsignedInt
  repReward         Float    @default(0) @db.Float
  talentPointReward Int      @default(0) @db.UnsignedInt
  shopId            Int?
  requiredQuestId   Int?

  isStoryQuest Boolean @default(false)
  // ADDED: Link quest to a story chapter for organization
  chapterId      Int?
  story_chapter  story_chapter? @relation(fields: [chapterId], references: [id])
  orderInChapter Int?

  shop            shop?             @relation(fields: [shopId], references: [id])
  quest           quest?            @relation("questToquest", fields: [requiredQuestId], references: [id])
  other_quest     quest[]           @relation("questToquest")
  quest_progress  quest_progress[]
  quest_reward    quest_reward[]
  quest_objective quest_objective[]

  @@unique([chapterId, orderInChapter])
  @@index([requiredQuestId])
  @@index([shopId])
}
```

// MODIFIED: story_episode is now simplified content, not a progression node

```prisma
model story_episode {
  id          Int               @id @default(autoincrement())
  name        String            @db.VarChar(255)
  description String?           @db.Text
  episodeType StoryEpisodeType  // For filtering/flavor (NARRATIVE, CHOICE, etc.)

  content Json  // Contains dialogue, scenes, etc.
  choices Json? // Defines player options and consequences for CHOICE types

  // ADDED: A one-to-one link back to the objective that triggers this episode
  objectiveId     Int?             @unique
  quest_objective quest_objective? @relation(fields: [objectiveId], references: [id])
}
```

## V2 Features: Advanced Story System

### Story Flags System

**Purpose**: Enable complex branching narratives and persistent choice consequences

**Core Features**:

- **Persistent State**: Story flags track player choices and story state across episodes
- **Branching Logic**: Episodes can have different content based on active flags

- **Character Relationships**: Track relationship levels with NPCs
- **World State**: Track changes to the game world based on story progression

### V2 Story Content Types

```typescript
// Enhanced episode content with choice consequences
export interface StorySceneChoice {
    id: string;
    text: string;
    consequence?: string;
    nextSceneId?: number;
    storyFlags?: string[]; // Flags set when choice is made
    requirements?: {
        level?: number;
        storyFlags?: string[]; // Flags required to see choice
    };
}

// Enhanced episode rewards
export interface StoryEpisodeRewards {
    xp?: number;
    cash?: number;
    items?: { itemId: number; quantity: number }[];
    storyFlags?: string[]; // Flags set when episode completes
}

// Enhanced episode choices
export type StoryEpisodeChoices = Record<
    string,
    {
        text: string;
        consequences: {
            storyFlags?: string[]; // Flags set by this choice
            nextEpisodeId?: number;
            rewards?: StoryEpisodeRewards;
        };
    }
>;
```

### V2 Implementation Plan

**Phase 1: Story Flags Foundation**

- Restore story flags to database schema
- Implement story flag tracking and management
- Add story flag validation and unlocking logic

**Phase 2: Choice System**

- Implement choice consequence processing
- Add branching narrative support

**Phase 3: Advanced Features**

- Story journal with choice history
- Character relationship tracking
- Complex story flag combinations
- Story flag-based quest unlocking

**Phase 4: Content Creation Tools**

- Admin interface for story flag management
- Choice tree visualization
- Story flag dependency mapping
- Branching narrative testing tools
